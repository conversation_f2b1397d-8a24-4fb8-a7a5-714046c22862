/**
 * @file 工作空间列表
 * <AUTHOR>
 */

import {FC, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {ExclamationCircle2} from '@baidu/xicon-react-bigdata';
import RefreshButton from '@components/RefreshButton';
import {
  Button,
  Link,
  Pagination,
  Search,
  Table,
  toast,
  Dropdown,
  Menu,
  Modal,
  Loading,
  Empty,
  Tooltip
} from 'acud';
import {ColumnsType} from 'acud/lib/table';
import {Ellipsis, Clipboard} from '@baidu/bce-react-toolkit';
import cx from 'classnames';
import {useRequest} from 'ahooks';
import {useNavigate} from 'react-router-dom';
import EditModal from './components/CreateModal';
import {deleteWorkspace, queryWorkspaceList, IQueryWorkspaceListParams, IWorkspace} from '@api/workspace';
import {formatTime} from '@utils/moment';
import {WorkspaceStatusMap, EWorkspaceStatus} from '../constants';
import {OrderType} from '@utils/enums';
import adminEmptyImg from '@assets/png/workspace/admin-empty.png';
import userEmptyImg from '@assets/png/workspace/user-empty.png';
import IconSvg from '@components/IconSvg';
import EdapEntry from '@components/EdapEntry';
import {useSelector} from 'react-redux';

import styles from './index.module.less';
import store, {IAppState} from '@store/index';
import {Privilege} from '@api/permission/type';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import AuthButton from '@components/AuthComponents/AuthButton';
import {workspaceMenus} from '../../index';
import urls from '@utils/urls';
import {WorkspacePrivileges} from '@utils/auth';

const Workspace: FC = () => {
  const navigate = useNavigate();
  const [workspaceList, setWorkspaceList] = useState<Array<IWorkspace>>([]);
  const [queryWorkspaceListLoading, setQueryWorkspaceListLoading] = useState(true); // 初始化loading状态，避免闪烁
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [keyword, setKeyword] = useState('');
  const [statusFilterValue, setStatusFilterValue] = useState();
  const [createTimeSortValue, setCreateTimeSortValue] = useState<OrderType>();
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 20,
    total: 0
  });

  const isSysAdmin = useSelector((state: IAppState) => state.globalAuthSlice.systemAdmin);

  // 初始化查询
  useEffect(() => {
    getWorkspaceList();
  }, []);

  // 跳转详情页
  const jumpDetail = useCallback((id) => {
    navigate(`/manage-workspace/detail?id=${id}`);
  }, []);

  // 跳转空间内工作区
  const jumpWorkspace = useCallback((id, privileges) => {
    const privilegeValue = Object.fromEntries(
      WorkspacePrivileges.map((key: Privilege) => [key, privileges.includes(key)])
    );
    store.dispatch({
      type: 'globalAuth/updateWorkspacePermission',
      payload: privilegeValue
    });

    const link =
      workspaceMenus.filter((item) => item.isNavMenu).find((item) => privilegeValue?.[item.privilege])?.key ||
      urls.workArea;

    window.open(window.location.pathname + `#${link}?workspaceId=${id}`, '_blank');
  }, []);

  // 点击新建按钮
  const onClickCreateBtn = useCallback(() => {
    setIsModalVisible(true);
  }, []);

  // 删除工作空间
  const runDeleteWorkspace = useCallback(
    (params) => {
      return deleteWorkspace(params);
    },
    [deleteWorkspace]
  );

  // 查询工作空间列表
  const runWorkspaceList = useCallback(
    (params) => {
      setQueryWorkspaceListLoading(true);
      queryWorkspaceList(params).then((res) => {
        if (res.success) {
          setWorkspaceList(res?.result?.items || []);
          setPagination((prev) => ({...prev, total: res?.result?.total || 0}));
        }
        setQueryWorkspaceListLoading(false);
      });
    },
    [queryWorkspaceList]
  );

  // 获取工作空间列表
  const getWorkspaceList = useCallback(
    (params: IQueryWorkspaceListParams = {}) => {
      if (params.pageNo) {
        setPagination((prev) => ({...prev, pageNo: params.pageNo!}));
      }
      runWorkspaceList({
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize,
        name: keyword,
        status: statusFilterValue,
        ...(createTimeSortValue != null
          ? {
              order: createTimeSortValue,
              orderBy: 'createdAt'
            }
          : {}),
        ...params
      });
    },
    [runWorkspaceList, pagination, keyword, statusFilterValue, createTimeSortValue]
  );

  // 关闭弹窗
  const handleCloseModal = useCallback(() => {
    setIsModalVisible(false);
  }, []);

  // 数据总数
  const showTotal = useCallback(() => {
    return `共${pagination.total}条`;
  }, [pagination.total]);

  // 监听点击刷新按钮
  const onClickRefreshBtn = useCallback(() => {
    getWorkspaceList();
  }, [getWorkspaceList]);

  // 搜索
  const onConfirmSearch = useCallback(
    (value: string) => {
      setKeyword(value);
      getWorkspaceList({
        name: value,
        pageNo: 1
      });
    },
    [getWorkspaceList]
  );

  // 监听表格发生变化
  const onTableChange = useCallback(
    (...args: any) => {
      // 筛选
      const status = args?.[1]?.status?.[0] || null;
      setStatusFilterValue(status);

      let createTimeSortValue = args?.[2]?.order;
      createTimeSortValue =
        createTimeSortValue === 'ascend'
          ? OrderType.asc
          : createTimeSortValue === 'descend'
            ? OrderType.desc
            : null;
      setCreateTimeSortValue(createTimeSortValue);

      getWorkspaceList({
        pageNo: 1,
        status,
        ...(createTimeSortValue != null
          ? {
              order: createTimeSortValue,
              orderBy: 'createdAt'
            }
          : {order: undefined, orderBy: undefined})
      });
    },
    [getWorkspaceList]
  );

  // 更多操作
  const handleMoreAction = useCallback(
    (event, record) => {
      switch (event.key) {
        case 'detail':
          jumpDetail(record.id);
          break;
        case 'delete':
          Modal.confirm({
            title: '删除空间',
            content: '您是否确认删除当前空间？删除后相关作业及日志将无法恢复',
            okText: '删除',
            onOk() {
              return runDeleteWorkspace({id: record.id}).then((res) => {
                if (res.success) {
                  toast.success({message: '删除成功', duration: 5});
                  getWorkspaceList();
                }
              });
            },
            onCancel() {}
          });
          break;
        default:
          break;
      }
    },
    [runDeleteWorkspace, jumpDetail, getWorkspaceList]
  );

  const columns: ColumnsType<IWorkspace> = useMemo(() => {
    return [
      {
        title: '空间名称',
        dataIndex: 'name',
        key: 'name',
        width: '30%',
        ellipsis: {showTitle: false}, // 去除系统title
        render: (name, record) => {
          const openDisabled = !record.privileges.some((item) =>
            [Privilege.Modify, Privilege.FullControl, Privilege.Browse].includes(item)
          );
          return (
            <Ellipsis tooltip={name}>
              {openDisabled ? <span>{name}</span> : <Link onClick={() => jumpDetail(record.id)}>{name}</Link>}
            </Ellipsis>
          );
        }
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: '20%',
        filterMultiple: false,
        filters: Object.keys(WorkspaceStatusMap).map((key) => ({
          value: key,
          text: WorkspaceStatusMap[key]
        })),
        render: (status, record: IWorkspace) => {
          return (
            <Tooltip title={status === EWorkspaceStatus.ERROR ? '存储路径被删除或损坏' : ''}>
              <span className={cx(styles['workspace-status'], styles[status])}>
                {WorkspaceStatusMap[status]}
                {status === EWorkspaceStatus.ERROR && (
                  <ExclamationCircle2 theme="line" color="#f33e3e" size={12} strokeLinejoin="round" />
                )}
              </span>
            </Tooltip>
          );
        }
      },
      {
        title: '存储地址',
        dataIndex: 'storageLocation',
        key: 'storageLocation',
        width: '30%',
        ellipsis: {showTitle: false},
        render: (storageLocation) => {
          return (
            <div
              style={{
                display: 'flex',
                position: 'relative'
              }}
            >
              <div style={{maxWidth: '90%'}}>
                <Ellipsis
                  tooltip={storageLocation}
                  tooltipProps={{placement: 'top', overlayClassName: styles['large-tooltip-width600']}}
                >
                  {storageLocation}
                </Ellipsis>
              </div>
              <Clipboard
                text={storageLocation}
                successMessage="复制成功"
                className={styles['container-clipboard']}
              >
                <Button icon={<IconSvg type="copy" />} size="small" type="actiontext"></Button>
              </Clipboard>
            </div>
          );
        }
      },
      {
        title: '描述',
        dataIndex: 'desc',
        key: 'desc',
        width: 200,
        ellipsis: {showTitle: false},
        render: (desc) => {
          return desc ? <Ellipsis tooltip={desc}>{desc}</Ellipsis> : '-';
        }
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        key: 'creator',
        width: 100,
        ellipsis: {showTitle: false},
        render: (creator) => {
          return <Ellipsis tooltip={creator}>{creator}</Ellipsis>;
        }
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 160,
        sorter: true,
        render: (time) => formatTime(time)
      },
      {
        title: '操作',
        width: 130,
        fixed: 'right',
        render: (record: IWorkspace) => {
          const openDisabled = !record.privileges.includes(Privilege.Browse);
          const spaceError = record.status === EWorkspaceStatus.ERROR;
          return (
            <div className={styles['td-operation-bar']}>
              <Tooltip
                title={
                  spaceError ? '该空间状态为【不可用】，无法进入！' : openDisabled ? '暂无此空间权限' : ''
                }
              >
                <Link
                  onClick={() => jumpWorkspace(record.id, record.privileges)}
                  className={styles['td-operation-open']}
                  disabled={spaceError || openDisabled}
                >
                  打开
                  <IconSvg type="open" size={16} />
                </Link>
              </Tooltip>

              <Dropdown
                overlayStyle={{width: 100}}
                overlay={
                  <Menu onClick={(e) => handleMoreAction(e, record)}>
                    <Menu.Item key="detail">详情</Menu.Item>
                    <AuthMenuItem isAuth={record.privileges.includes(Privilege.FullControl)} key="delete">
                      删除
                    </AuthMenuItem>
                  </Menu>
                }
              >
                <IconSvg type="more" size={16} className={styles['td-operation-more']} />
              </Dropdown>
            </div>
          );
        }
      }
    ];
  }, [isSysAdmin, jumpDetail, jumpWorkspace, handleMoreAction]);

  // 初次加载数据全局loading
  if (workspaceList.length === 0 && queryWorkspaceListLoading && !keyword) {
    return <Loading loading={queryWorkspaceListLoading} />;
  }

  return (
    <div className={styles['workspace-out-wrapper']}>
      {/* EDAP 入口 （仅公有云+bj/bd展示）*/}
      <EdapEntry />
      <div className={styles['workspace-wrapper']}>
        {(!workspaceList || pagination.total === 0) && !keyword && !statusFilterValue ? (
          <div className={styles['empty-container']}>
            <div className={styles['empty-block']}>
              <div className={styles['empty-title']}>工作空间</div>
              <div className={styles['empty-desc']}>你还没创建任何空间，可创建工作空间开启使用</div>
              <AuthButton
                isAuth={isSysAdmin}
                type="primary"
                icon={<IconSvg type="add" />}
                onClick={onClickCreateBtn}
                size="large"
                style={{width: 176}}
              >
                创建工作空间
              </AuthButton>
              <img src={adminEmptyImg} alt="empty" className={styles['empty-img']} width={800} />
            </div>
          </div>
        ) : (
          <>
            <div className={styles['workspace-wrapper-header']}>工作空间</div>
            <div className={styles['operation-container']}>
              <div className={styles['left-btn-container']}>
                <Search
                  placeholder="请输入空间名称进行搜索"
                  className={styles['search-container']}
                  allowClear
                  onSearch={onConfirmSearch}
                  style={{width: 240}}
                />
              </div>
              <div className={styles['right-container']}>
                <RefreshButton onClick={onClickRefreshBtn} style={{width: 32}}></RefreshButton>
                <AuthButton
                  isAuth={isSysAdmin}
                  type="primary"
                  icon={<IconSvg type="add" />}
                  onClick={onClickCreateBtn}
                >
                  创建工作空间
                </AuthButton>
              </div>
            </div>

            <Table
              className={styles['acud-table-wrapper']}
              dataSource={workspaceList}
              columns={columns}
              rowKey="id"
              // scroll={pagination.total > 0 ? {x: 1228, y: 'calc(100vh - 266px)'} : undefined}
              loading={{
                loading: queryWorkspaceListLoading,
                size: 'small'
              }}
              pagination={false}
              onChange={onTableChange}
              locale={
                keyword // 搜索状态下为空时，走默认空态
                  ? null
                  : {
                      emptyText: (
                        <Empty
                          className="test"
                          style={{
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center'
                          }}
                          image={null}
                          description={<div className={styles['empty-title']}>暂⽆任何空间权限</div>}
                          children={
                            <>
                              <div className={styles['empty-desc']}>
                                请联系您的⼯作空间管理员，确保您已被分配到此⼯作空间
                              </div>
                              <img
                                src={userEmptyImg}
                                alt="empty"
                                className={styles['empty-img']}
                                width={800}
                              />
                            </>
                          }
                        />
                      )
                    }
              }
            />
            {pagination.total > 0 && (
              <div className={styles['pagination-container']}>
                <Pagination
                  showSizeChanger={true}
                  showQuickJumper={true}
                  showTotal={showTotal}
                  current={pagination.pageNo}
                  total={pagination.total}
                  defaultPageSize={20}
                  onChange={(page, pageSize = 20) => {
                    setPagination((prev) => ({
                      ...prev,
                      pageNo: page,
                      pageSize: pageSize
                    }));
                    getWorkspaceList({
                      pageNo: page,
                      pageSize: pageSize
                    });
                  }}
                />
              </div>
            )}
          </>
        )}
        <EditModal
          isModalVisible={isModalVisible}
          handleCloseModal={handleCloseModal}
          getWorkspaceList={getWorkspaceList}
        />
      </div>
    </div>
  );
};

export default Workspace;
