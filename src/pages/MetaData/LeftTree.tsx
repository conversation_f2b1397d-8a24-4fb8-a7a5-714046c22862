import React, {
  forwardRef,
  useImperativeHandle,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import {useNavigate} from 'react-router-dom';
import {Refresh, Plus1} from '@baidu/xicon-react-bigdata';
import {Button, Loading, Search, Tooltip, Tree, Dropdown, Menu, Divider, Space} from 'acud';
import IconSvg from '@components/IconSvg';
import {useAsyncEffect, usePrevious, useRequest} from 'ahooks';
import {throttle} from 'lodash';
import urls from '@utils/urls';
import {LeftTreeSettingMenuItems, LeftTreeAddMenuItems} from './config';

import * as http from '@api/metaRequest';

import ModalCreateName from '@components/MetaCreateModal/ModalCreateName';
import UploadVolumeModal from '@components/UploadVolumeModal';

import {WorkspaceContext} from '../index';
import {IUrlStateHand<PERSON>, EnumNodeType} from './index';
import {joinNode<PERSON><PERSON>, transformToacudTreeData} from './helper';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {RULE} from '@utils/regs';
import {MetaCnNameMap} from './config';
import {CatalogType} from '@api/metaRequest';
import {PanelEnum} from './partial/PanelCatalog';
import {useSelector} from 'react-redux';
import {IAppState} from '@store/index';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import {Privilege} from '@api/permission/type';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';

interface DataNode {
  title: string | React.ReactNode;
  key: string;
  isLeaf?: boolean;
  children?: DataNode[];
}

export enum CatalogPermType {
  // 有权限
  HAS = 'catalogs',
  // 无权限
  NO = 'unassignedCatalogs'
}

// 请求 catalog list
const httpCatalogFun = async (workspaceId) => {
  // 根据类型排过序 后续开发需注意
  const res = await http.getCatalogList(workspaceId);
  const catalogMap = res?.result;
  return catalogMap || {catalogs: [], unassignedCatalogs: []};
};

// 请求 schema list
const httpScheamFun = async (workspaceId, catalogName) => {
  const schemaRes = await http.getSchemaList(workspaceId, {catalogName});
  const schemaList = schemaRes.result.schemas;
  return schemaList || [];
};

// 请求 table list
const httpTableFun = async (workspaceId, catalogName, schemaName) => {
  const tableRes = await http.getTableList(workspaceId, {catalogName, schemaName});
  const tableList = tableRes.result.tables;
  return tableList || [];
};

// 请求 volume list
const httpVolumeFun = async (workspaceId, catalogName, schemaName) => {
  const volumeRes = await http.getVolumeList(workspaceId, {catalogName, schemaName});
  const volumeList = volumeRes.result.volumes;
  return volumeList || [];
};

// 请求 operator list
const httpOperatorFun = async (workspaceId, catalogName, schemaName) => {
  const operatorRes = await http.getOperatorList(workspaceId, {catalogName, schemaName, workspaceId});
  const operatorList = operatorRes.result.operators;
  return operatorList || [];
};

// 请求 dataset list
const httpDatasetFun = async (workspaceId, catalogName, schemaName) => {
  const datasetRes = await http.getDatasetOrModelList(workspaceId, http.EnumMetaType.DATASETS, {
    workspaceId,
    catalogName,
    schemaName
  });
  const datasetList = datasetRes.result.datasets;
  return datasetList || [];
};

// 请求 model list
const httpModelFun = async (workspaceId, catalogName, schemaName) => {
  const modelRes = await http.getDatasetOrModelList(workspaceId, http.EnumMetaType.MODELS, {
    workspaceId,
    catalogName,
    schemaName
  });
  const modelList = modelRes.result.models;
  return modelList || [];
};

// trans API 数据为 Tree 数据格式
const transApiData2TreeData = (catalogMap: http.ICatalogRes) => {
  return [
    {
      key: CatalogPermType.HAS,
      title: '本空间数据',
      disabled: true,
      children: catalogMap.catalogs.map((item) => ({
        title: (
          <TextEllipsis width={'calc(100% - 24px)'} tooltip={item.name}>
            {item.name}
          </TextEllipsis>
        ),
        key: `${item.name}`,
        isDoris: item.type === CatalogType.DORIS
      }))
    },
    ...(catalogMap.unassignedCatalogs?.length
      ? [
          {
            key: CatalogPermType.NO,
            title: '非本空间数据',
            disabled: true,
            children: catalogMap.unassignedCatalogs.map((item) => ({
              title: (
                <TextEllipsis width={'calc(100% - 24px)'} tooltip={item.name}>
                  {item.name}
                </TextEllipsis>
              ),
              key: `${item.name}`,
              isDoris: item.type === CatalogType.DORIS,
              isUnassigned: true,
              children: [
                {
                  key: `${item.name}#>${CatalogPermType.NO}`,
                  title: '暂无权限访问',
                  disabled: true,
                  isUnassigned: true,
                  isLeaf: true
                }
              ]
            }))
          }
        ]
      : [])
  ];
};

// 更新对应 Tree Node 的 children
const updateTreeData = (list: DataNode[], key: React.Key, children: DataNode[]): DataNode[] =>
  list.map((node) => {
    if (node.key === key) {
      return {
        ...node,
        children
      };
    }
    if (node.children) {
      return {
        ...node,
        children: updateTreeData(node.children, key, children)
      };
    }
    return node;
  });

// Tree节点显示对应图标
export const iconSelectFun = (e) => {
  const str = e.data.key;
  const separator = '#>';
  let count = 0;
  let pos = str.indexOf(separator);
  let type = '';

  while (pos !== -1) {
    count++;
    const i = str.indexOf(separator, pos + separator.length);
    if (count == 2) {
      type = 'meta-' + str.slice(pos + 2, i);
    }
    pos = i;
  }

  const isUnassigned = e.data.isUnassigned;
  const imageName = e.data.isDoris ? 'meta-doris' : 'meta-iceberg';

  // 本空间数据和非本空间数据节点不显示图标
  if ([CatalogPermType.HAS, CatalogPermType.NO].includes(str)) {
    return null;
  }
  // 通过 #> 定位是哪一层级节点
  switch (count) {
    case 0:
      return str === CatalogType.SYSTEM ? (
        <IconSvg type="meta-system" size={16} color="#6c6d70" />
      ) : isUnassigned ? (
        <IconSvg type={`${imageName}-forbidden`} size={16} color="#6c6d70" />
      ) : (
        <IconSvg type={imageName} size={16} color="#6c6d70" />
      );
    case 1:
      return isUnassigned ? (
        <IconSvg type="meta-forbidden" size={16} color="#6c6d70" />
      ) : (
        <IconSvg type="meta-schema" size={16} color="#6c6d70" />
      );
    case 2:
      return null;
    case 3:
      return <IconSvg type={type} size={16} color="#6c6d70" />;
  }
};

const klass = 'meta-data-left-tree';

const originExpandedKeys = [CatalogPermType.HAS, CatalogPermType.NO];

const LeftTree = (props: IUrlStateHandler, ref: any) => {
  const navigate = useNavigate();

  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);

  // 权限相关
  const workspacePermission = useSelector((state: IAppState) => state.globalAuthSlice.workspacePermission);
  const integrationPermission = useWorkspaceAuth([Privilege.IntegrationMenu]);

  const {urlState, changeUrlFun, changeUrlFunReplace, hasMetastore, canWrite} = props;
  const {catalog, schema, type, node} = urlState;

  const [treeData, setTreeData] = useState<any>([]);

  const [selectedKeys, setSelectedKeys] = useState<string[]>(() => []);
  const selectPreKey = usePrevious(selectedKeys);

  const [expandedKeys, setExpandedKeys] = useState<string[]>(originExpandedKeys);
  // 搜索值
  const [searchVal, setSearchVal] = useState<string>('');

  // 搜索值变更监听
  const onSearchChange = useCallback((e) => {
    const tempValue = e.target.value;
    setSearchVal(tempValue);
    if (tempValue) {
      return;
    }
    // 把输入框内容为空时，主动调用一下刷新逻辑
    onRefresh();
  }, []);

  // 重置无权限标识
  const resetUnassigned = useCallback(
    (catalogMap: http.ICatalogRes) => {
      if (catalog) {
        const curUnassigned = catalogMap.unassignedCatalogs?.findIndex((item) => item.name === catalog);
        changeUrlFunReplace({
          isUnassigned: ~curUnassigned ? true : undefined,
          tab: ~curUnassigned ? PanelEnum.WORKSPACE : undefined
        });
      }
    },
    [catalog]
  );

  // 初始化 url 中未有 catalog 参数情况
  useAsyncEffect(async () => {
    if (!catalog) {
      treeData.length && setTreeData([]);
      // 清空搜索
      searchVal && setSearchVal('');
      const catalogMap = await httpCatalogFun(urlState.workspaceId || workspaceId);
      setTreeData(transApiData2TreeData(catalogMap));
      resetUnassigned(catalogMap);
      setSelectedKeys([CatalogType.SYSTEM]);
      changeUrlFunReplace({
        catalog: CatalogType.SYSTEM
      });
    }
  }, [catalog]);

  // 树节点搜索
  const {loading: treeLoading, run: onSearch} = useRequest(
    async (value) => {
      if (value) {
        const res = await http.searchMetastore(workspaceId, {filter: value, workspaceId});
        const data = res?.result || {catalogs: [], unassignedCatalogs: []};
        const treeData = transformToacudTreeData(data, value);
        setTreeData(treeData);
      } else {
        onRefresh();
      }
    },
    {
      manual: true
    }
  );

  // 树选择
  const onTreeSelect = (Keys, info) => {
    // 取消选择时，不变化
    if (Keys.length === 0) {
      selectPreKey && setSelectedKeys(selectedKeys);
      return;
    }
    // 点击文件夹时不变化
    // 判断 Keys[0] 中包含两个 '#>'时，说明点击的是文件夹
    if (Keys[0].split('#>').length === 3) {
      setSelectedKeys(selectedKeys);
      const index = expandedKeys?.indexOf(Keys[0]);
      if (index > -1) {
        setExpandedKeys([...expandedKeys.slice(0, index), ...expandedKeys.slice(index + 1)]);
      } else {
        setExpandedKeys([...expandedKeys, Keys[0]]);
      }
      return;
    }

    setSelectedKeys(Keys);

    const selectKey = Keys[0] || '';
    const [catalog, schema, type, node] = selectKey.split('#>');

    // 当点击分类文件夹时，不更新 urlState
    if (type && !node) {
      return;
    }

    changeUrlFun({
      catalog,
      schema,
      type,
      node,
      isUnassigned: info.node.isUnassigned ? true : undefined,
      tab: info.node.isUnassigned ? PanelEnum.WORKSPACE : undefined,
      isDoris: info.node.isDoris ? true : undefined,
      version: undefined,
      path: undefined
    });
  };

  // 树展开
  const onExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
  };

  // 异步加载 Tree Data
  const onLoadData = useCallback(
    async ({key, children}: any) => {
      if (children && children.length > 0) {
        return;
      }
      //根据 key 中 的 #> 分割出来判断第几层, 也用 #> 来拼接 key
      if (key.indexOf('#>') < 0) {
        // 请求 第二层 schema
        const schema = await httpScheamFun(workspaceId, key);
        setTreeData((origin) => {
          return updateTreeData(
            origin,
            key,
            schema.map((item) => {
              return {
                title: (
                  <TextEllipsis
                    width={'calc(100% - 24px)'}
                    tooltip={item}
                    keyword={searchVal}
                    keywordColor="#2468F2"
                  >
                    {item}
                  </TextEllipsis>
                ),
                key: `${key}#>${item}`
              };
            })
          );
        });
      } else {
        const [catalogName, schemaName] = key.split('#>');

        // 使用 Promise.allSettled 处理多个异步请求
        const [tableResult, volumeResult, operatorResult, datasetResult, modelResult] =
          await Promise.allSettled([
            httpTableFun(workspaceId, catalogName, schemaName),
            httpVolumeFun(workspaceId, catalogName, schemaName),
            httpOperatorFun(workspaceId, catalogName, schemaName),
            httpDatasetFun(workspaceId, catalogName, schemaName),
            httpModelFun(workspaceId, catalogName, schemaName)
          ]);

        // 处理每个请求的结果，如果失败则设置为空数组
        const tableList = tableResult.status === 'fulfilled' ? tableResult.value : [];
        const volumeList = volumeResult.status === 'fulfilled' ? volumeResult.value : [];
        const operatorList = operatorResult.status === 'fulfilled' ? operatorResult.value : [];
        const datasetList = datasetResult.status === 'fulfilled' ? datasetResult.value : [];
        const modelList = modelResult.status === 'fulfilled' ? modelResult.value : [];

        setTreeData((origin) => {
          return updateTreeData(origin, key, [
            ...(tableList?.length
              ? [
                  {
                    title: `${MetaCnNameMap['Table']}${tableList?.length ? `（${tableList.length}）` : ''}`,
                    key: `${key}#>${EnumNodeType.TABLE}`,
                    children: tableList.map((item) => {
                      return {
                        title: (
                          <TextEllipsis
                            width={'calc(100% - 24px)'}
                            tooltip={item}
                            keyword={searchVal}
                            keywordColor="#2468F2"
                          >
                            {item}
                          </TextEllipsis>
                        ),
                        key: `${key}#>${EnumNodeType.TABLE}#>${item}`,
                        isLeaf: true
                      };
                    })
                  }
                ]
              : []),
            ...(volumeList?.length
              ? [
                  {
                    title: `${MetaCnNameMap['Volume']}${volumeList?.length ? `（${volumeList.length}）` : ''}`,
                    key: `${key}#>${EnumNodeType.VOLUME}`,
                    children: volumeList.map((item) => {
                      return {
                        title: (
                          <TextEllipsis
                            width={'calc(100% - 24px)'}
                            tooltip={item}
                            keyword={searchVal}
                            keywordColor="#2468F2"
                          >
                            {item}
                          </TextEllipsis>
                        ),
                        key: `${key}#>${EnumNodeType.VOLUME}#>${item}`,
                        isLeaf: true
                      };
                    })
                  }
                ]
              : []),
            ...(operatorList?.length
              ? [
                  {
                    title: `${MetaCnNameMap['Operator']}${operatorList?.length ? `（${operatorList.length}）` : ''}`,
                    key: `${key}#>${EnumNodeType.OPERATOR}`,
                    children: operatorList.map((item) => {
                      return {
                        title: (
                          <TextEllipsis
                            width={'calc(100% - 24px)'}
                            tooltip={item}
                            keyword={searchVal}
                            keywordColor="#2468F2"
                          >
                            {item}
                          </TextEllipsis>
                        ),
                        key: `${key}#>${EnumNodeType.OPERATOR}#>${item}`,
                        isLeaf: true
                      };
                    })
                  }
                ]
              : []),
            ...(datasetList?.length
              ? [
                  {
                    title: `${MetaCnNameMap['Dataset']}${datasetList?.length ? `（${datasetList.length}）` : ''}`,
                    key: `${key}#>${EnumNodeType.DATASET}`,
                    children: datasetList.map((item) => {
                      return {
                        title: (
                          <TextEllipsis
                            width={'calc(100% - 24px)'}
                            tooltip={item}
                            keyword={searchVal}
                            keywordColor="#2468F2"
                          >
                            {item}
                          </TextEllipsis>
                        ),
                        key: `${key}#>${EnumNodeType.DATASET}#>${item}`,
                        isLeaf: true
                      };
                    })
                  }
                ]
              : []),
            ...(modelList?.length
              ? [
                  {
                    title: `${MetaCnNameMap['Model']}${modelList?.length ? `（${modelList.length}）` : ''}`,
                    key: `${key}#>${EnumNodeType.MODEL}`,
                    children: modelList.map((item) => {
                      return {
                        title: (
                          <TextEllipsis
                            width={'calc(100% - 24px)'}
                            tooltip={item}
                            keyword={searchVal}
                            keywordColor="#2468F2"
                          >
                            {item}
                          </TextEllipsis>
                        ),
                        key: `${key}#>${EnumNodeType.MODEL}#>${item}`,
                        isLeaf: true
                      };
                    })
                  }
                ]
              : [])
          ]);
        });
      }
    },
    [searchVal, workspaceId]
  );

  // 根据 url 参数 加载 Tree 节点
  const requestTreeChildren = async () => {
    const catalogMap = await httpCatalogFun(workspaceId);
    setTreeData(transApiData2TreeData(catalogMap));
    resetUnassigned(catalogMap);
    if (schema) {
      await onLoadData({key: catalog});
      if (type && node) {
        await onLoadData({key: `${catalog}#>${schema}`});
      }
    }
  };

  // 根据 URL 来回显 TreeData 并 选中节点
  useAsyncEffect(async () => {
    // 更新 TreeData（仅页面初始化加载数据）
    if (catalog && !treeData.length) {
      await requestTreeChildren();
    } else if (!catalog) {
      // 无catalog 则为刷新或者重新进入 需要清空选中和展开的节点
      setSelectedKeys([]);
      setExpandedKeys([...originExpandedKeys]);
      return;
    }
    const nodeKeys = joinNodeKey(urlState);
    // 更新 Tree 选中的 selectKeys
    setSelectedKeys([nodeKeys.selectedKeys || CatalogType.SYSTEM]);
    // 更新 Tree 展开的节点 expandedKeys
    setExpandedKeys(Array.from(new Set([...expandedKeys, ...nodeKeys.expandedKeys])));
  }, [urlState]);

  // 重新加载
  const onRefresh = useCallback(
    throttle(async () => {
      // 先置空
      setTreeData([]);
      if (searchVal) {
        // 存在搜索条件，转给搜索逻辑处理
        onSearch(searchVal);
        return;
      }
      await requestTreeChildren();
      const nodeKeys = joinNodeKey(urlState);
      setSelectedKeys([nodeKeys.selectedKeys!]);
      setExpandedKeys([...nodeKeys.expandedKeys, ...originExpandedKeys]);
    }, 1000),
    [searchVal, urlState]
  );

  // 新建 Catalog 弹窗
  const [visibleModal, setVisibleModal] = useState<boolean>(false);
  const createSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFun({
        catalog: formData.name,
        schema: '',
        type: '',
        node: ''
      });
      onRefresh();
    },
    [changeUrlFun]
  );

  // 设置 Tree 树滚动范围最大高度
  const [maxHeight, setMaxHeight] = useState<string>();
  const leftBoxDomRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleResize = () => {
      const height = leftBoxDomRef.current?.offsetHeight ?? 0;
      setMaxHeight(height ? `${height - 95}px` : '100%'); // 更新最大高度
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const treeProps = useMemo(
    () => ({
      ...(searchVal ? {} : {expandedKeys: expandedKeys}),
      defaultExpandAll: searchVal ? true : false
    }),
    [searchVal, expandedKeys]
  );

  useImperativeHandle(ref, () => ({
    refresh: onRefresh
  }));

  // 渲染工具栏操作按钮 下拉菜单项
  const renderMenuItems = useCallback(
    (items: {key: string; label?: React.ReactNode; iconType?: string; authName?: string}[]) =>
      items.map((item) => {
        if (item.key.includes('divider')) {
          return <Divider key={item.key} />;
        }
        if (item.authName) {
          const integrationTag = item.key.includes('dataIntegration');
          const permissionCools = integrationTag ? integrationPermission : workspacePermission;
          return (
            <>
              <AuthMenuItem
                key={item.key}
                isAuth={permissionCools[item.authName]}
                className="flex items-center justify-between"
              >
                {item.label}
                {item.iconType && <IconSvg type={item.iconType} color="#151b26" size={14} />}
              </AuthMenuItem>
            </>
          );
        }
        return (
          <>
            <Menu.Item key={item.key} className="flex items-center justify-between">
              {item.label}
              {item.iconType && <IconSvg type={item.iconType} color="#151b26" size={14} />}
            </Menu.Item>
          </>
        );
      }),
    [workspacePermission, integrationPermission]
  );

  const settingMenu = renderMenuItems(LeftTreeSettingMenuItems); // 设置按钮下拉菜单
  const addMenu = useMemo(() => renderMenuItems(LeftTreeAddMenuItems), [renderMenuItems]); // 添加按钮下拉菜单

  const handleSettingClick = ({key}) => {
    switch (key) {
      case 'connectionManagement':
        navigate(`${urls.connection}`);
        break;
      default:
        break;
    }
  };
  const handleAddClick = ({key}) => {
    switch (key) {
      case 'dataIntegration':
        window.open(`${window.location.pathname}#${urls.integration}?workspaceId=${workspaceId}`, '_blank');
        break;
      case 'upload':
        setShowUploadModal(true);
        break;
      case 'createCatalog':
        setVisibleModal(true);
        break;
      case 'createConnection':
        navigate(`${urls.connection}?workspaceId=${workspaceId}&mode=create`);
        break;
      default:
        break;
    }
  };

  const controllerRef = useRef(new AbortController());
  const [showUploadModal, setShowUploadModal] = useState<boolean>(false);

  // 关闭上传文件弹窗
  const onCancelUpload = () => {
    setShowUploadModal(false);
  };

  // 上传文件弹窗关闭后重置 AbortController
  const afterCloseUploadFile = () => {
    controllerRef.current.abort();
    controllerRef.current = new AbortController();
  };

  useEffect(() => {
    return () => {
      controllerRef.current.abort();
    };
  }, []);

  return (
    <div className={`${klass}`} ref={leftBoxDomRef}>
      <header className={`${klass}-header`}>
        <h3>元数据</h3>
        <div className={`${klass}-header-operations`}>
          <Dropdown
            overlayClassName={`${klass}-header-operations-setting-menu`}
            overlay={<Menu onClick={handleSettingClick}>{settingMenu}</Menu>}
          >
            <Tooltip placement="top" title="管理">
              <Button type="text" icon={<IconSvg type="setting" color="#151b26" size={16} />}></Button>
            </Tooltip>
          </Dropdown>

          <Tooltip placement="top" title="刷新目录">
            <Button
              type="text"
              icon={<IconSvg type="refresh-2-arrow" color="#151b26" size={16} />}
              onClick={onRefresh}
            ></Button>
          </Tooltip>

          <Dropdown
            overlayClassName={`${klass}-header-operations-add-menu`}
            overlay={<Menu onClick={handleAddClick}>{addMenu}</Menu>}
          >
            <Tooltip placement="top" title={hasMetastore ? `添加数据` : `无法添加数据，请先配置元存储`}>
              <Button
                type="text"
                disabled={!hasMetastore}
                icon={<IconSvg type="add" color="#151b26" size={16} />}
              ></Button>
            </Tooltip>
          </Dropdown>

          {/* <Tooltip
            placement="top"
            title={
              hasMetastore
                ? `新建${MetaCnNameMap['Catalog']}`
                : `无法创建${MetaCnNameMap['Catalog']}，请先配置元存储`
            }
          >
            <Button
              type="text"
              onClick={() => setVisibleModal(true)}
              disabled={!hasMetastore || !canWrite}
              icon={<Plus1 theme="line" color="#151b26" size={16} strokeLinejoin="round" />}
            ></Button>
          </Tooltip> */}
        </div>
      </header>
      <Search
        value={searchVal}
        onChange={onSearchChange}
        placeholder="请输入名称搜索"
        allowClear
        onSearch={onSearch}
      />
      <Loading loading={treeLoading}>
        <div className={`${klass}-content`} style={{maxHeight: maxHeight, overflowY: 'auto'}}>
          {treeLoading ? null : (
            <Tree
              treeData={treeData}
              selectedKeys={selectedKeys}
              {...treeProps}
              icon={iconSelectFun}
              onSelect={onTreeSelect}
              loadData={onLoadData}
              onExpand={onExpand}
            />
          )}
        </div>
      </Loading>
      {/** 创建 Catalog 弹窗 */}
      <ModalCreateName
        visible={visibleModal}
        onCancel={() => setVisibleModal(false)}
        title="Catalog"
        requestFun={http.createCatalog}
        requestParams={{workspaceId}}
        successCallback={createSuccessFun}
        nameRules={[
          {
            validator: async (_, value) => {
              // 校验特殊字符和长度限制
              if (!RULE.specialName64.test(value)) {
                return Promise.reject(new Error(RULE.specialName64Text));
              }
              // 异步校验Volume名称是否重复，复用查询接口 silent模式
              const res = await http.getCatalogDetail(workspaceId, `${value}`, true);
              if (res.success && res.result?.catalog?.id) {
                return Promise.reject(new Error('该Catalog名称已存在，请重新输入'));
              }
              return Promise.resolve();
            }
          }
        ]}
      />

      {/** 上传文件到 Volume */}
      <UploadVolumeModal
        showPathSelect
        visible={showUploadModal}
        onCancel={onCancelUpload}
        onCreateSuccessCallback={onRefresh}
        controller={controllerRef.current}
        afterCloseUploadFile={afterCloseUploadFile}
      />
    </div>
  );
};

export default forwardRef(LeftTree);
