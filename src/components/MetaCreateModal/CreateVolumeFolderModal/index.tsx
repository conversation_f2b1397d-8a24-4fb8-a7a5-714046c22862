/**
 * 新建弹窗：Catalog、Schema
 * * 注意：
 * * * 1、不包括 Volume、Table、Operator的弹窗，该弹窗为简单的名称 + 描述
 * <AUTHOR>
 */

import React, {useContext, useMemo, useState} from 'react';
import {Alert, Form, Input, Modal, Space, toast} from 'acud';
import {createVolumeFolder} from '@api/metaRequest';
import {WorkspaceContext} from '@pages/index';
import TextEllipsis from '@components/TextEllipsisTooltip';

interface CreateVolumeFolderProps {
  // volume 全名 {catalogName}.{schemaName}.{volumeName}
  fullName: string;
  // 创建文件夹的路径
  path: string;
  visible: boolean; // 弹窗可见
  onCancel: () => void; // 取消回调
  successCallback: (folder: string) => void; // 请求成功后回调
}

const folderRule = [
  {required: true, message: `请输入文件夹名称`},
  {
    validator: async (_, value) => {
      // 目录命名规范：
      // 请使用符合要求的UTF-8字符，长度在1-254个字符之间
      // /用于分隔路径，可快速创建子目录，但不要以/或\开头，不要出现连续的/
      // 不允许出现名为..的子目录
      const reg =
        /^(?!\/|\\)(?!.*\/\/)(?!.*(?:^|\/)(?:\.\.?)(?:\/|$))[\x20-\x7E\u00A0-\uD7FF\uE000-\uFFFD]{1,254}(?<!\/)$/;
      if (!reg.test(value)) {
        return Promise.reject(new Error('请按下列规则输入'));
      }
      return Promise.resolve();
    }
  }
];

const extraInfo = (
  <div className="mt-[12px]">
    <div>1. 请使用符合要求的UTF-8字符，长度在1-254个字符之间 </div>
    <div>2. /用于分隔路径，可快速创建子目录，但不要以/或\开头，不要出现连续的/ </div>
    <div>3. 不允许出现名为.和..的子目录</div>
  </div>
);

/**
 * 创建 volume 下 folder弹窗
 */
const CreateVolumeFolderModal: React.FC<CreateVolumeFolderProps> = ({
  fullName,
  path,
  visible,
  onCancel,
  successCallback
}) => {
  const [form] = Form.useForm();
  const {workspaceId} = useContext(WorkspaceContext);
  // 确定按钮 loading
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [folder, setFolder] = useState<string>();

  const handleCancel = () => {
    setConfirmLoading(false);
    form.resetFields();
    onCancel();
  };

  const handleOk = async () => {
    try {
      await form.validateFields();
    } catch {
      return;
    }
    setConfirmLoading(true);
    try {
      const res = await createVolumeFolder(fullName, `${path}/${folder}`, workspaceId);
      if (res?.success != true) {
        setConfirmLoading(false);
        return;
      }
      toast.success({
        message: '创建成功！',
        duration: 5
      });
      // 请求成功后的回调
      successCallback(folder);
      handleCancel();
    } catch {
      setConfirmLoading(false);
      return;
    }
  };

  const onInputChange = (e) => setFolder(e.target.value);

  const addonBefore = useMemo(() => {
    return <TextEllipsis tooltip={path}>{path}</TextEllipsis>;
  }, [path]);

  return (
    <Modal
      title="在数据卷中创建文件夹"
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={confirmLoading}
    >
      <Alert
        message="支持创建单个文件夹和多层级文件夹，使用斜杠(/)分隔，可创建多层级文件夹。"
        type="info"
        showIcon
        className="mb-[12px]"
      />
      <Form form={form}>
        <Form.Item name="folder" rules={folderRule} extra={extraInfo} keepDisplayExtra={true}>
          <Input addonBefore={addonBefore} onChange={onInputChange} placeholder="新文件夹名称" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateVolumeFolderModal;
